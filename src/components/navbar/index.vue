<template>
    <wd-navbar title="标题" leftArrow safeAreaInsetTop custom-style="background-color: transparent !important;">
        <template #title>
            <view class="search-box">
                <wd-drop-menu>
                    <wd-drop-menu-item :icon-size="20" v-model="value1" :options="option" @change="handleChange" ></wd-drop-menu-item>
                </wd-drop-menu>
            </view>
        </template>
        <template #right>
            <wd-icon @click="addMember" name="add" size="16" color="#1D232E" />
        </template>
    </wd-navbar>
</template>
<script setup>
import { ref } from 'vue'
const value1 = ref(0)
const option = ref([
  { label: 'Choose a baby', value: 0 },
  { label: 'Emma', value: 1 },
  { label: 'Kitty', value: 2 }
])
const handleChange = (value) => {
    
}
</script>