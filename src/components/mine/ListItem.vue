<script setup lang="ts">
const props = defineProps<{
  icon: string
  title: string
  type?: 'arrow' | 'none'
}>()
</script>

<template>
  <view class="list-item">
    <view class="icon">
      <image
        :src="props.icon"
        mode="scaleToFill"
      />
    </view>
    <view class="title">
      {{ props.title }}
    </view>
    <view v-if="props.type !== 'none'" class="arrow">
      <image
        src="/static/mine/arrow.png"
        mode="scaleToFill"
      />
    </view>
    <slot />
  </view>
</template>

<style scoped lang="scss">
.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 686rpx;
  height: 112rpx;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  box-sizing: border-box;
  background-color: #fafafa;
  margin-top: 32rpx;

  .icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;

    image {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }
  }
  .title {
    font-family: var(--font-family-regular);
    font-weight: $font-weight-regular;
    font-size: $font-size-lg;
    color: #616161;
    flex: 1;
    text-align: left;
    padding-left: 32rpx;
  }
  .arrow {
    width: 48rpx;
    height: 48rpx;
    image {
      width: 48rpx;
      height: 48rpx;
    }
  }
}
</style>
