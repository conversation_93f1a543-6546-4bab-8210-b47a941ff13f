<template>
  <view class="my-avatar">
    <image :src="avatar" mode="scaleToFill" />
    <slot class='handle-slot' />
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  avatar: {
    type: String,
    default: ''
  }
})
</script>

<style scoped lang="scss">
.my-avatar {
  width: 220rpx;
  height: 220rpx;
  border-radius: 50%;
  margin: 0 auto;
  background-color: #F2994A80;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  image {
    width: 212rpx;
    height: 212rpx;
    border-radius: 50%;
  }

  .handle-slot {
    position: absolute;
    bottom: 0;
    right: 0;
  }
}
</style>