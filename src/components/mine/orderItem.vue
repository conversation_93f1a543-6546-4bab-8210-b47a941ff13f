<script setup lang="ts">
import type { PropType } from 'vue'
import { ref } from 'vue'

// 定义商品类型
interface Product {
  id: number
  title: string
  price: number
  img: string
  num: number
}

// 定义订单项类型
interface OrderItem {
  id: number
  orderNumber: string
  product: Product[]
  paymentTime: string
  deliveryTime: string
  status: string
}

const props = defineProps({
  data: {
    type: Array as PropType<OrderItem[]>,
    default: () => [],
  },
})

const emit = defineEmits(['handleConract'])
</script>

<template>
  <view v-for="(item) in props.data" :key="item.id" class="order-item">
    <!-- 订单编号 -->
    <view class="order-item-title">
      Order Number: {{ item.orderNumber }}
    </view>

    <!-- 商品列表 -->
    <view v-for="product in item.product" :key="product.id" class="order-item-content">
      <view class="order-item-name">
        <view class="order-item-name-text">
          {{ product.title }}
        </view>
        <view class="order-item-price">
          ${{ product.price.toFixed(2) }}
        </view>
      </view>
      <view class="order-item-img">
        <image :src="product.img" mode="scaleToFill" />
        <view class="order-item-btns">
          <view class="order-item-num">
            <image src="/static/mine/plus.png" mode="scaleToFill" />
            <text>{{ product.num }}</text>
          </view>
          <wd-button custom-class="order-item-btn">
            <view class="order-item-btn-text">
              View instructions
            </view>
          </wd-button>
        </view>
      </view>
    </view>

    <!-- 订单时间 -->
    <view class="order-item-time">
      <view class="order-item-time-title">
        Payment time:
      </view>
      <view>{{ item.paymentTime }}</view>
    </view>
    <view class="order-item-time">
      <view class="order-item-time-title">
        Delivery time:
      </view>
      <view>{{ item.deliveryTime }}</view>
    </view>

    <!-- 订单按钮 -->
    <view v-if="!['6', '7'].includes(item.status)" class="order-item-btn-box">
      <wd-button custom-class="order-item-btn">
        <view class="order-item-btn-text">
          Refund
        </view>
      </wd-button>
      <wd-button custom-class="order-item-btn-details">
        View order details
      </wd-button>
    </view>
    <view v-else class="order-item-btn-box">
      <view />
      <wd-button custom-class="order-item-btn-details contact-btn" @click="emit('handleConract')">
        Contact the platform
      </wd-button>
    </view>
  </view>
</template>

<style scoped lang="scss">
.order-item {
  width: 686rpx;
  box-sizing: border-box;
  font-family: var(--font-family-regular);
  padding: 24rpx;
  background-color: #f9fafb;
  border-radius: 40rpx;
  margin: 0 auto;
  margin-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;

  .order-item-title {
    font-weight: $font-weight-regular;
    font-size: $font-size-lg;
    padding-bottom: 24rpx;
    height: 38rpx;
    color: #475467;
    border-bottom: 1px dashed #D0D5DD
  }

  .order-item-content {

    .order-item-name {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 54rpx;

      .order-item-name-text {
        font-weight: $font-weight-regular;
        font-size: $font-size-heading-sm;
        color: #001833;
      }

      .order-item-price {
        font-family: var(--font-family-regular);
        font-weight: $font-weight-bold;
        font-size: $font-size-heading-base;
        color: #FF7465;
      }
    }

    .order-item-img {
      display: flex;
      justify-content: space-between;
      width: 638rpx;
      height: 240rpx;
      margin-top: 24rpx;

      image {
        width: 240rpx;
        height: 240rpx;
        border-radius: 40rpx;
      }

      .order-item-btns {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .order-item-btn {
          width: 288rpx;
          height: 80rpx;
          border-radius: 100rpx;
          border: 2px solid transparent;

          background-image:
            linear-gradient(#fff1e6, #fff1e6),
            /* 内部背景色 */
            linear-gradient(92.74deg, #FFB173 9.43%, #FF9844 96.54%);
          /* 渐变边框 */

          background-origin: border-box;
          background-clip: padding-box, border-box;

          .order-item-btn-text {
            width: 100%;
            font-size: $font-size-lg;
            background: $gradient-color-light;
            -webkit-background-clip: text;
            color: transparent;
            -webkit-text-fill-color: transparent; // iOS 兼容性处理
            text-shadow: 1px 1px 1px 0px #0000001A;
          }
        }
      }

      .order-item-num {
        font-size: $font-size-heading-sm;
        display: flex;
        align-items: center;
        height: 46rpx;
        justify-content: end;
        gap: 16rpx;
        color: #757575;
        line-height: 46rpx;

        image {
          width: 36rpx;
          height: 36rpx;
        }
      }
    }
  }

  .order-item-time {
    display: flex;
    align-items: center;
    font-weight: $font-weight-regular;
    font-size: $font-size-heading-sm;
    height: 54rpx;
    gap: 24rpx;
    color: #001833;

    .order-item-time-title {
      width: 240rpx;
      color: #475467;
    }
  }

  .order-item-btn-box {
    display: flex;
    justify-content: space-between;
    height: 100rpx;

    .order-item-btn {
      width: 238rpx;
      height: 100rpx;
      border-radius: 100rpx;
      border: 1px solid transparent;
      background-image:
        linear-gradient(#fff, #fff),
        /* 内部背景色 */
        linear-gradient(92.74deg, #FFB173 9.43%, #FF9844 96.54%);
      /* 渐变边框 */
      background-origin: border-box;
      background-clip: padding-box, border-box;

      .order-item-btn-text {
        width: 100%;
        font-size: $font-size-heading-sm;
        color: #f8852d;
        text-shadow: 1px 1px 1px 0px #0000001A;
      }
    }

    .order-item-btn-details {
      width: 360rpx;
      height: 100rpx;
      border-radius: 100rpx;
      background: $gradient-color-light;
      box-shadow: 0px 8px 16px 0px #FDA05559;
      font-size: $font-size-heading-sm;
    }
    .contact-btn {
      width: 430rpx;
    }
  }
}
</style>
