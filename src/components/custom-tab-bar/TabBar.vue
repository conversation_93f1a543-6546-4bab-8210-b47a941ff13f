<script setup lang="ts">
import { computed, ref, toRefs } from 'vue'
import TabItem from './TabItem.vue'
import tabConfig from '@/config/tabbar' // 配置数据
import { Z_INDEX_RULES } from '@/utils/zIndex';
// 页面z-index取值
// const pageZIndex = computed(() => Z_INDEX_RULES.PAGE.TAB_BAR);
const pageZIndex = ref(100)
pageZIndex.value = Z_INDEX_RULES.PAGE.TAB_BAR 
// console.log('pageZIndex', Z_INDEX_RULES.PAGE.TAB_BAR)
// 定义 props
const props = defineProps<{
  safeAreaBottom: number, // 安全区域高度
  select: (item: any, index: number) => void,
}>()

import { useNavBar } from '@/utils/height'
const {
  statusBarHeight,
  navBarHeight,
  placeholderHeight
} = useNavBar()
// console.log('获取到的高度', statusBarHeight, navBarHeight, placeholderHeight)
// 使用 toRefs 将 props 转换为响应式引用
const { safeAreaBottom } = toRefs(props)

const tabList = ref(tabConfig)
const currentPath = ref(tabList.value[0].pagePath)


// 安全区域适配（全面屏底部留空）
const tabBarStyle = computed(() => ({
  paddingBottom: `${safeAreaBottom.value}px`,
  boxShadow: '0 -2px 10px rgba(0,0,0,0.05)',
}))

// 切换Tab（含特殊按钮逻辑）
function selectTabBar(item) {
  if(!item.isSpecial){
    currentPath.value = item.pagePath
  }
  props.select(item)
}
</script>

<template>
  <view class="tab-bar" :style="tabBarStyle">
    <TabItem
      v-for="item in tabList"
      :key="item.pagePath"
      :item="item"
      :active="currentPath === item.pagePath"
      @click="selectTabBar(item)"
    />
  </view>
</template>

<style scoped>
.tab-bar {
  font-family: var(--font-family-title);
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  height: 150rpx; /* 标准高度 */
  background: white;
  z-index: 1000;
  border-radius: 40rpx 40rpx 0 0;
  padding: 0 32rpx;
}
</style>
