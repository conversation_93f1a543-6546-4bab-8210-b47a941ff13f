<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  type: string
}>()

// 定义事件
const emit = defineEmits<{
  'bell-click': [source: string]
}>()

const isFloating = ref(false)

function handleClick() {
  if (props.type === 'mother')
    return
  isFloating.value = true

  setTimeout(() => {
    isFloating.value = false

    emit('bell-click', 'mother-modal')
  }, 600)
}

// 提示
function handleBellClick() {
  emit('bell-click', 'mother')
}
</script>

<template>
  <view class="mother" :style="{ zIndex: type === 'mother' ? '9999' : '0' }" @click="handleClick">
    <image src="/static/accompany/mother.png" :class="{ floating: isFloating }" mode="scaleToFill" />

    <view v-show="!type" class="bell" @click.stop="handleBellClick">
      <image src="/static/accompany/Notification.png" mode="scaleToFill" />
    </view>
  </view>
  <view v-show="type === 'mother'" class="dialog">
    <text class="dialog-title">
      Encyclopedia
    </text> will be updated in <text class="dialog-title">
      29 days
    </text>. Missed
    knowledge
    won't come back, and neither will MEDALS.
  </view>
</template>

<style scoped lang="scss">
.mother {
  position: absolute;
  width: 280rpx;
  height: 302rpx;
  top: 264rpx;
  right: 32rpx;

  image {
    width: 100%;
    height: 100%;
  }

  .bell {
    width: 72rpx;
    height: 72rpx;
    position: absolute;
    left: 154rpx;
    top: -18rpx;
    animation: float 0.6s ease-in-out infinite;
  }
}

.dialog {
  position: absolute;
  width: 352rpx;
  height: 168rpx;
  background-size: 100% 100%;
  padding: 8rpx 24rpx 28rpx 20rpx;
  box-sizing: border-box;
  font-family: var(--font-family-regular);
  font-weight: $font-weight-regular;
  font-size: $font-size-xxls;
  line-height: 44rpx;
  color: #1d232e;
  text-align: left;
  z-index: 9999;
  top: 142rpx;
  right: -70rpx;
  background-image: url('/static/accompany/motherBox.png');

  .dialog-title {
    color: $color-primary;
  }
}

/* 定义点击后浮动动画 */
.floating {
  animation: float 0.6s ease-in-out;
}

@keyframes float {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}
</style>
