<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  answerCount: number
  questionCount: number
}>()

// 进度条
const progressWidth = computed(() => {
  return `${(props.answerCount / props.questionCount) * 100}%`
})

// 进度条颜色
const progressColor = computed(() => {
  if (props.answerCount < props.questionCount * 0.3) {
    return '#6EE39F'
  }
  if (props.answerCount < props.questionCount * 0.6) {
    return '#FACC15'
  }
  return '#F8852D'
})

// wow
const showWow = computed(() => {
  return props.answerCount === Math.round(props.questionCount * 0.36)
    || props.answerCount === Math.round(props.questionCount * 0.5)
    || props.answerCount === Math.round(props.questionCount * 0.76)
})
</script>

<template>
  <view class="progress">
    <view>
      {{ answerCount }} <text class="progress-count">
        / {{ questionCount }}
      </text>
    </view>
    <view class="progress-bar">
      <view class="progress-bar-inner" :style="{ width: progressWidth, backgroundColor: progressColor }">
        <view v-if="showWow" class="wow">
          <image src="/static/accompany/wow.png" mode="scaleToFill" />
        </view>
        {{ answerCount }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.progress {
  width: 686rpx;
  height: 96rpx;
  display: flex;
  flex-direction: column;
  align-items: end;
  justify-content: space-between;
  margin-top: 40rpx;
  font-family: var(--font-family-title);
  font-weight: $font-weight-bold;
  font-size: $font-size-heading-base;
  color: $color-primary;

  .progress-count {
    font-size: $font-size-md;
    color: #98a2b3;
  }

  .progress-bar {
    width: 100%;
    height: 32rpx;
    background-color: #eee;
    border-radius: 200rpx;

    .progress-bar-inner {
      height: 32rpx;
      min-width: 38rpx;
      border-radius: 200rpx;
      padding: 0 12rpx;
      box-sizing: border-box;
      font-size: $font-size-base;
      color: #fff;
      text-align: right;
      transition: all 0.5s ease;
      position: relative;

      .wow {
        position: absolute;
        bottom: 20px;
        right: -61rpx;
        width: 190rpx;
        height: 84rpx;

        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
