<template>
	<view class="father" @click="handleClick" :style="{ zIndex: type === 'father' ? '9999' : '0' }">
		<image src="/static/accompany/father.png" :class="{ 'floating': isFloating }" mode="scaleToFill" />

		<view class="bell" @click.stop="handleBellClick" v-show="!type">
			<image src="/static/accompany/Notification.png" mode="scaleToFill" />
		</view>

	</view>
	<view v-show="type === 'father'" class="dialog">
		The <text class="dialog-title">Parent Evaluation</text> will be updated in <text class="dialog-title">29
			days</text>. The missed knowledge won't come
		back, nor will the MEDALS.
	</view>

</template>

<script setup lang="ts">
import { ref } from 'vue'

const isFloating = ref(false)

// 定义事件
const emit = defineEmits<{
	'bell-click': [source: string]
}>()
const props = defineProps<{
	type: string
}>()

function handleClick() {
	if (props.type === 'father') return
	isFloating.value = true

	setTimeout(() => {
		isFloating.value = false

		uni.showToast({
			title: 'father',
			icon: 'none',
		})
	}, 600)
}

// 提示
function handleBellClick() {
	emit('bell-click', 'father')
}
</script>

<style scoped lang="scss">
.father {
	position: absolute;
	width: 188rpx;
	height: 442rpx;
	top: 78rpx;
	left: 0;

	image {
		width: 100%;
		height: 100%;
	}

	.bell {
		width: 72rpx;
		height: 72rpx;
		position: absolute;
		left: 160rpx;
		top: 32rpx;
		animation: float 0.6s ease-in-out infinite;
	}

}

.dialog {
	position: absolute;
	width: 352rpx;
	height: 168rpx;
	background-size: 100% 100%;
	padding: 8rpx 24rpx 28rpx 20rpx;
	box-sizing: border-box;
	font-family: var(--font-family-regular);
	font-weight: $font-weight-regular;
	font-size: $font-size-xxls;
	line-height: 44rpx;
	color: #1d232e;
	text-align: left;
	z-index: 9999;
	top: -26rpx;
	right: 48rpx;
	background-image: url('/static/accompany/fatherBox.png');

	.dialog-title {
		color: $color-primary;
	}
}

/* 定义点击后浮动动画 */
.floating {
	animation: float 0.6s ease-in-out;
}

@keyframes float {
	0% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.05);
	}

	100% {
		transform: scale(1);
	}
}
</style>