<template>
  <view class="child" @click="handleClick" :style="{ zIndex: type === 'child' ? '9999' : '0' }">
    <image src="/static/accompany/child.png" :class="{ 'floating': isFloating }" mode="scaleToFill" />

    <view class="bell" @click.stop="handleBellClick" v-show="!type">
      <image src="/static/accompany/Notification.png" mode="scaleToFill" />
    </view>
  </view>
  <view v-show="type === 'child'" class="dialog">
    The <text class="dialog-title">Children Evaluation</text> will be updated in <text class="dialog-title">29
      days</text>. Missed knowledge won't come back, nor will MEDALS.
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const isFloating = ref(false)

// 定义事件
const emit = defineEmits<{
  'bell-click': [source: string]
}>()
const props = defineProps<{
  type: string
}>()

function handleClick() {
  if (props.type === 'child') return
  isFloating.value = true

  setTimeout(() => {
    isFloating.value = false

    emit('bell-click', 'child-modal')
  }, 600)
}

// 提示
function handleBellClick() {
  emit('bell-click', 'child')
}
</script>

<style scoped lang="scss">
.child {
  position: absolute;
  width: 300rpx;
  height: 184rpx;
  bottom: 0rpx;
  left: 70rpx;

  image {
    width: 100%;
    height: 100%;
  }

  .bell {
    width: 72rpx;
    height: 72rpx;
    position: absolute;
    right: -16rpx;
    top: 12rpx;
    animation: float 0.6s ease-in-out infinite;
  }
}

.dialog {
  position: absolute;
  width: 352rpx;
  height: 168rpx;
  background-size: 100% 100%;
  padding: 8rpx 24rpx 28rpx 20rpx;
  box-sizing: border-box;
  font-family: var(--font-family-regular);
  font-weight: $font-weight-regular;
  font-size: $font-size-xxls;
  line-height: 44rpx;
  color: #1d232e;
  text-align: left;
  z-index: 9999;
  bottom: 138rpx;
  right: -54rpx;
  background-image: url('/static/accompany/childBox.png');

  .dialog-title {
    color: $color-primary;
  }
}

/* 定义点击后浮动动画 */
.floating {
  animation: float 0.6s ease-in-out;
}

@keyframes float {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}
</style>