<template>
  <view class="story-modal">
    <!-- title -->
    <view class="title">
      {{ introduce.title }}
    </view>

    <view class="qs-xp">
      <view class="qs">{{ introduce.qs }} Q's</view>
      <view class="divide"></view>
      <view class="xp">{{ introduce.xp }} XP</view>
    </view>

    <!-- 内容 -->
    <view class="content" v-html="introduce.content">
    </view>

    <!-- 按钮 -->
    <view class="btn-box">
      <wd-button custom-class="btn-item btn-start" @click="handleStart">Start</wd-button>
      <wd-button custom-class="btn-item btn-report" type="text" @click="handleReport">Report</wd-button>
    </view>

    <!-- 统计 -->
    <view class="statistics">
      A total of {{ Number(introduce.total).toLocaleString('en-US') }} people have taken this test,
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const emit = defineEmits<{
  (e: 'report'): void,
  (e: 'hide'): void
}>()
const props = defineProps<{
  type: 'child-modal' | 'mother-modal' | 'father-modal'
}>()

const introduceMap = ref<any>({
  'child-modal': {
    title: 'Children Evaluation',
    qs: 100,
    xp: 50,
    total: '267092',
    content: `This test is designed for children aged 3 to 12. Through multi-dimensional behavioral observation, it scientifically assesses children's personality traits and potential for development.\n\nThe test is based on the Four Images-Eight Type Theory and Child Development Psychology, combined with AI algorithm analysis. It constructs an assessment system from four dimensions: social inclination (introverted / extroverted), energy management (practical / intuitive), cognitive style (logical / creative), and behavioral pattern (active / cautious). It generates personalized personality profiles and development suggestions. The test results can assist parents and teachers in formulating educational plans and matching suitable learning methods and interest cultivation paths. The system adopts a dynamic assessment model, regularly updates data, tracks the trajectory of personality development, and helps children build confidence and optimize social skills during critical growth periods. The entire test process is presented in a child-friendly manner to ensure fun and safety, facilitating a comprehensive understanding of children's inner traits and achieving individualized education.`
  },
  'mother-modal': {
    title: 'Parent Evaluation'
  },
  'father-modal': {
    title: 'Children Evaluation'
  },
})
const introduce = computed(() => introduceMap.value[props.type])

const handleStart = () => {
  emit('hide')
  uni.navigateTo({
    url: '/subPackages/accompany/evaluation'
  })
}

const handleReport = () => {
  emit('hide')
  emit('report')
}
</script>

<style scoped lang="scss">
.story-modal {
  width: 598rpx;
  height: 800rpx;
  background-color: #fff;
  box-shadow: 10px 20px 46px 0px #9E774D24;
  border-radius: 32rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 56rpx;
  padding: 56rpx 48rpx;
  box-sizing: border-box;
  font-family: var(--font-family-title);
  text-align: center;

  .title {
    height: 58rpx;
    line-height: 58rpx;
    color: #212121;
    font-size: $font-size-heading-sm;
    font-weight: $font-weight-bold;
  }

  .qs-xp {
    display: flex;
    gap: 16rpx;
    color: #616161;
    justify-content: center;
    align-items: center;
    font-size: $font-size-md;

    .divide {
      width: 10rpx;
      height: 10rpx;
      background-color: #616161;
      border-radius: 50%;
    }

    .qs {
      font-weight: $font-weight-bold;
    }

    .xp {
      font-weight: $font-weight-regular;
      font-family: var(--font-family-regular);
    }
  }

  .content {
    overflow: auto;
    flex: 1;
    text-align: left;
    font-family: var(--font-family-light);
    font-size: $font-size-md;
    color: #1d232e;
    font-weight: $font-weight-light;
    white-space: pre-line;
    line-height: 150%;
    letter-spacing: 2%;
    vertical-align: middle;

    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
      color: transparent;
    }

    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
  }

  .btn-box {
    height: 220rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .btn-item {
      width: 100%;
      height: 100rpx;
      font-family: var(--font-family-regular);
      font-weight: $font-weight-regular;
      font-size: $font-size-heading-sm;
      line-height: 100%;
    }

    .btn-start {
      box-shadow: 0px 8px 16px 0px #FDA05559;
      background: $gradient-color-light;
    }

    .btn-report {
      color: $color-primary;
      text-shadow: 1px 1px 1px 0px #0000001A;
    }
  }

  .statistics {
    height: 32rpx;
    font-family: var(--font-family-light);
    font-size: $font-size-sm;
    color: #1d232e;
    font-weight: $font-weight-light;
  }
}
</style>