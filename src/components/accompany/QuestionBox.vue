<script setup lang="ts">
import { onMounted, reactive } from 'vue';

const props = defineProps<{
  currentIndex: number
  questionList: any[]
  type: string
}>()
const emit = defineEmits<{
  (e: 'next'): void
  (e: 'setAnswer', val: string): void
}>()

// 题库是否反转
const showBackArr = reactive<boolean[]>([])
onMounted(() => {
  props.questionList.forEach(() => showBackArr.push(false))
})

// 题目样式
function getQuestionClass(index: number) {
  if (index > props.currentIndex) {
    return 'followUp'
  }
  if (index < props.currentIndex) {
    return 'preposition'
  }
  if (index === props.questionList.length - 1) {
    return 'last'
  }
  return 'action'
}

// 题目标题
const stem = function () {
  if (props.type !== 'mother-modal') {
    return 'Question stem'
  } else {
    return 'MC'
  }
}

// 选中
function select(option: string, index: number) {
  showBackArr[index] = true
  emit('setAnswer', option)

  if (props.type !== 'mother-modal') {
    emit('next')
  }
}

// 是否为百科的已答状态
const isAnswered = function () {

}

</script>

<template>
  <view class="content">
    <view v-for="(item, index) in questionList" :key="item.id" class="question-box " :class="{
      'show-back': showBackArr[index],
      [getQuestionClass(index)]: true
    }">
      <view class="item" v-if="!showBackArr[index]">
        <text class="stem">
          {{ stem() }}
        </text>

        <!-- 题目 -->
        <view class="title">
          {{ item.title }}
        </view>

        <!-- 选项 -->
        <view v-for="(option) in item.answer" :key="option" class="option"
          :class="{ active: item.correctAnswer === option }" @click="select(option, index)">
          <view class="divide" />{{ option }}
        </view>
      </view>

      <!-- 卡片背面 -->
      <view class="item" v-else>
        <text class="stem">
          Science
        </text>

        <!-- 科学解释内容 -->
        <view class="science-content">
          {{ item.science || 'No scientific explanation available for this question.' }}
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.content {
  width: 686rpx;
  flex: 1;
  margin-top: 40rpx;
  position: relative;
  display: flex;
  align-items: center;

  .show-back {
    transform: rotate3d(0, 1, 0, 180deg);
  }

  .question-box {
    position: absolute;
    height: 100%;
    transition: all 1s ease-in-out;

    .item {
      width: 640rpx;
      height: 100%;
      overflow-y: auto;
      border-radius: 40rpx;
      border: 1px solid #FFCAA1;
      background-color: #fff;
      box-shadow: 6px 6px 24px 0px #E7670026;
      padding: 40rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 32rpx;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
      }

      scrollbar-width: none;
      /* Firefox */
      -ms-overflow-style: none;
      /* IE 10+ */

      .stem {
        padding: 0 20rpx;
        height: 46rpx;
        background-color: $color-primary;
        color: #fff;
        border-radius: 10rpx;
        line-height: 46rpx;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        font-family: var(--font-family-regular);
        text-align: center;
        white-space: nowrap;
        max-width: max-content;
      }

      .title {
        font-family: var(--font-family-regular);
        font-weight: $font-weight-regular;
        font-size: $font-size-lg;
        line-height: 150%;
        letter-spacing: 2%;
      }

      .option {
        width: 100%;
        height: 80rpx;
        border-radius: 40rpx;
        background-color: #f9fafb;
        border: 1px solid #f9fafb;
        font-family: var(--font-family-regular);
        font-weight: $font-weight-regular;
        font-size: $font-size-lg;
        letter-spacing: 2%;
        line-height: 80rpx;
        vertical-align: middle;
        padding: 0 20rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        color: #1d232e;

        .divide {
          width: 8rpx;
          height: 8rpx;
          background-color: #1d232e;
          border-radius: 50%;
          margin: 0 16rpx;
        }
      }

      .active {
        border-color: $color-primary;
        color: $color-primary;

        .divide {
          background-color: $color-primary;
        }
      }

      .science-content {
        flex: 1;
        font-family: var(--font-family-regular);
        font-weight: $font-weight-regular;
        font-size: $font-size-lg;
        line-height: 150%;
        letter-spacing: 2%;
        color: #1d232e;
        background-color: #f9fafb;
        padding: 32rpx;
        border-radius: 20rpx;
        border: 1px solid #e4e7ec;
      }
    }


  }

  .action {
    z-index: 10;
  }

  .followUp {
    transform: translateX(46rpx);
    z-index: 5;
    height: calc(100% - 80rpx);
    box-sizing: border-box;
  }

  .preposition {
    z-index: 15;
    transform: translateX(-150%);
  }

  .last {
    transform: translateX(23rpx);
  }
}
</style>
