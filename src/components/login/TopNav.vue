<script setup lang="ts">
import type { Component } from 'vue'

const props = defineProps<{
  icon?: string | Component
}>()

function goBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="login-type">
    <view class="separator">
      <wd-icon v-if="props.icon === 'back'" name="thin-arrow-left" size="14px" @click="goBack()" />
      <slot name="left" v-else />
    </view>
    <view>
      <slot name="middle" />
    </view>
    <view class="separator">
      <slot name="right"></slot>
    </view>
  </view>
</template>

<style scoped lang="scss">
.login-type {
  font-size: $font-size-md;
  line-height: 44rpx;
  font-family: var(--font-family-regular);
  font-weight: $font-weight-regular;
  padding: 22rpx 24rpx;
  height: 88rpx;
  box-sizing: border-box;
  // margin-top: 88rpx;
  display: flex;
  justify-content: space-between;

  .separator {
    width: 136rpx;
  }
}
</style>
