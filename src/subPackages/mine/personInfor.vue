<template>
  <view class="page-container app-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Personal information
        </view>
      </template>
    </TopNav>

    <view class="content">
      <!-- 头像 -->
      <person-item title="Profile picture">
        <view class="input-container" @click="handleAvatarClick">
          <view class="avatar">
            <image :src="userInfo.avatar" mode="scaleToFill" />
          </view>
          <view class="arrow">
            <image src="/static/mine/arrow.png" mode="scaleToFill" />
          </view>
        </view>
      </person-item>
      <!-- 昵称 -->
      <person-item title="Nickname">
        <view class="input-container">
          <wd-input :focus="isFocused" type="text" v-model="userInfo.nickname" :no-border="true" placeholder="Nickname"
            style="background-color: transparent;text-align: right;" class="input-placeholder"
            @blur="handleNicknameChange" />
          <view class="arrow" @click="setFocus(true)">
            <image src="/static/mine/arrow.png" mode="scaleToFill" />
          </view>
        </view>
      </person-item>
      <!-- 性别 -->
      <person-item title="Gender">
        <view class="gender-box">
          <image src="/static/mine/boy.png" mode="scaleToFill" :style="{ opacity: userInfo.sex === 1 ? 1 : 0.3 }"
            @click="handleGenderChange('1')" />
          <image src="/static/mine/girl.png" mode="scaleToFill" :style="{ opacity: userInfo.sex === 0 ? 1 : 0.3 }"
            @click="handleGenderChange('0')" />
        </view>
      </person-item>
      <!-- 生日 -->
      <person-item title="Birthday">
        <picker mode="date" :value="userInfo.birthday" @change="handleDateChange">
          <view class="input-container">
            <view class="uni-input input-placeholder" :class="{ 'placeholder-active': !userInfo.birthday }">
              {{ userInfo.birthday || 'Please choose' }}
            </view>
            <view class="arrow">
              <image src="/static/mine/arrow.png" mode="scaleToFill" />
            </view>
          </view>
        </picker>
      </person-item>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useAuthStore, type IUserInfo } from '@/stores/useAuthStore';
import { nextTick, ref, watch } from 'vue';
import { mineApi } from '@/api/mine';

const authStore = useAuthStore()

const userInfo = ref({ ...authStore.userInfo })
// 监听 authStore.userInfo 变化，自动同步到 userInfo
watch(
  () => authStore.userInfo,
  (newVal) => {
    userInfo.value = { ...newVal }
  },
  { deep: true }
)

// 聚焦昵称
let isFocused = ref(false)
// 聚焦昵称
function setFocus(focus: boolean) {
  if (focus) {
    isFocused.value = false // 先设为 false
    nextTick(() => {
      isFocused.value = true // 再设为 true，触发聚焦
    })
  } else {
    isFocused.value = false
  }
}

// 修改昵称
async function handleNicknameChange() {
  try {
    const data: any = await mineApi.setInfo({ field: 'nickname', value: userInfo.value.nickname })
    authStore.userInfo = data
  } catch (error) {
    console.log(error);
  }
}


// 选择头像
function handleAvatarClick() {
  uni.navigateTo({
    url: '/subPackages/mine/profilePicture',
  })
}

// 选择日期
async function handleDateChange(e: any) {
  try {
    const data: any = await mineApi.setInfo({ field: 'birthday', value: e.detail.value })
    authStore.userInfo = data
  } catch (error) {
    console.log(error);
  }
}
// 选择性别
async function handleGenderChange(e: string) {
  try {
    const data: any = await mineApi.setInfo({ field: 'sex', value: e })
    authStore.userInfo = data
  } catch (error) {
    console.log(error);
  }
  // userInfo.value.sex = e
}
</script>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);

  .content {
    width: 686rpx;
    margin: 0 auto;
    margin-top: 40rpx;

    .input-placeholder {
      font-family: var(--font-family-regular);
      font-weight: $font-weight-regular;
      font-size: $font-size-md;
    }

    .placeholder-active {
      color: #98A2B3;

    }


    .input-container {
      display: flex;
      align-items: center;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;

        image {
          width: 80rpx;
          height: 80rpx;
        }
      }
    }

    .gender-box {
      display: flex;
      flex-direction: row-reverse;
      gap: 32rpx;
      width: 150rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .arrow {
      width: 48rpx;
      height: 48rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }
}
</style>