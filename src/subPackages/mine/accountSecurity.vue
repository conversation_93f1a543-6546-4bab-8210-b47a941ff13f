<template>
  <view class="page-container app-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Account security
        </view>
      </template>
    </TopNav>

    <view class="content">
      <!-- Password modification -->
      <person-item title="Password modification" @click="handlePasswordModification">
        <view class="input-container">
          <view class="arrow">
            <image src="/static/mine/arrow.png" mode="scaleToFill" />
          </view>
        </view>
      </person-item>
      <!-- Bind the mobile phone -->
      <person-item title="Bind the mobile phone" @click="handleBindMobilePhone">
        <view class="input-container">
          <view class="arrow">
            <image src="/static/mine/arrow.png" mode="scaleToFill" />
          </view>
        </view>
      </person-item>
    </view>
  </view>
</template>

<script setup lang="ts">
function handleBindMobilePhone() {
  uni.navigateTo({
    url: '/subPackages/mine/mobilePhone'
  })
}
function handlePasswordModification() {
  uni.navigateTo({
    url: '/subPackages/mine/passwordModification',
  })
}
</script>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .content {
    width: 686rpx;
    margin: 0 auto;
    margin-top: 40rpx;

    .arrow {
      width: 48rpx;
      height: 48rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }
}
</style>