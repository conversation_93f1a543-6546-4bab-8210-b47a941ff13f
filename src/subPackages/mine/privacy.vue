<script setup lang="ts">
import { ref } from 'vue'

const privacyList = ref([
  {
    id: 1,
    title: 'Privacy Policy I',
  },
  {
    id: 2,
    title: 'Privacy Policy II',
  },
  {
    id: 3,
    title: 'Privacy Policy III',
  },
])
function handlePrivacy(item: any) {
  uni.navigateTo({
    url: `/subPackages/mine/privacyDetail?id=${item.id}&title=${item.title}`,
  })
}
</script>

<template>
  <view class="page-container app-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Privacy
        </view>
      </template>
    </TopNav>

    <view class="content">
      <!-- Privacy Policy -->
      <person-item v-for="item in privacyList" :key="item.id" :title="item.title" @click="handlePrivacy(item)">
        <view class="arrow">
          <image src="/static/mine/arrow.png" mode="scaleToFill" />
        </view>
      </person-item>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .content {
    width: 686rpx;
    margin: 0 auto;
    margin-top: 40rpx;

    .arrow {
      width: 48rpx;
      height: 48rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }
}
</style>
