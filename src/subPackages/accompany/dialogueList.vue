<template>
  <view class="page-container app-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Dialogue
        </view>
      </template>
      <template #right>
        <view class="right-slot" @click="handleAddClick">
          <wd-icon name="add" size="40rpx" color="#667085"></wd-icon>
        </view>
      </template>
    </TopNav>

    <view class="content">
      <view class="input-container" v-if="!(!parData.isLoading && list.length === 0)">
        <InputComponent v-model:value="inputValue" @enter="(val) => handleSeed(val)">
          <view class="search-icon">
            <image src="/static/mine/Search.png" mode="scaleToFill" />
          </view>
        </InputComponent>
      </view>

      <scroll-view class="list" scroll-y :show-scrollbar="false" :refresher-enabled="true" :refresher-threshold="100"
        refresher-default-style="white" :refresher-triggered="parData.isRefreshing" refresher-background="transparent"
        @scrolltolower="() => handleScrolltolower()" @refresherrefresh="() => handleRefresh()">
        <DialogboxItem v-for="item in list" :key="item.id" :item="item" />

        <!-- 加载更多提示 -->
        <view v-if="parData.isLoading" class="loading-tip">
          <text>Loading...</text>
        </view>

        <!-- 没有更多数据提示 -->
        <view v-if="parData.isLastPage && list.length > 0" class="no-more-tip">
          <text>No more data</text>
        </view>

        <!-- 空状态 -->
        <view v-if="!parData.isLoading && list.length === 0" class="empty-state">
          <image src="/static/mine/Notification.png" class="empty-image" mode="aspectFit" />
          <text class="empty-title">
            Dialogue with History
          </text>
          <text class="empty-text">
            You need to create a new conversation, and the content after the conversation will be recorded here
          </text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 输入框
const inputValue = ref('');
const parData = ref(
  {
    page: 1,
    isLastPage: false,
    isLoading: false,
    isRefreshing: false,
  }
)

const list = ref<any[]>([
  // {
  //   id: 1,
  //   title: 'Child psychological gender',
  //   content: 'The budding of psychological gender in children usually begins around the age of 2 to 3aroundaround.',
  // }
]);

// 搜索
const handleSeed = (val: string) => {
  if (!val.trim()) return;
  console.log(val);
};
// 上拉分页加载
async function handleScrolltolower() {
  const currentTab = parData.value
  if (currentTab.isLastPage || currentTab.isLoading) {
    return
  }
  currentTab.page++
  await getDialogueList()
}

// 下拉刷新
async function handleRefresh() {
  const currentTab = parData.value
  currentTab.isRefreshing = true
  currentTab.page = 1
  currentTab.isLastPage = false
  await getDialogueList(true)
}

// 获取会话列表
async function getDialogueList(isRefresh = false) {
  const currentTab = parData.value
  if (currentTab.isLoading)
    return

  currentTab.isLoading = true

  try {
    console.log('getDialogueList', {
      page: currentTab.page,
      isRefresh,
    })

    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000))
    const mockData: any[] = []

    // 模拟最后一页（第3页后没有数据）
    if (currentTab.page >= 3) {
      currentTab.isLastPage = true
    }

    if (isRefresh || currentTab.page === 1) {
      list.value = mockData
    }
    else {
      list.value.push(...mockData)
    }

    console.log(`数据加载成功`, list.value.length)
  }
  catch (error) {
    console.error('获取列表失败', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  }
  finally {
    currentTab.isLoading = false
    if (currentTab.isRefreshing) {
      currentTab.isRefreshing = false
    }
  }
}

// 新会话
function handleAddClick() {
  uni.navigateBack()
}

</script>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;
  // padding-top: 40rpx;

  .right-slot {
    display: flex;
    justify-content: end;
  }

  .content {
    flex: 1;
    padding-top: 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: auto;

    .search-icon {
      width: 48rpx;
      height: 48rpx;
      margin-left: 24rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .input-container {
      height: 120rpx;
    }

    .list {
      padding-top: 10rpx;
      flex: 1;
      overflow: auto;
      width: 686rpx;
      box-sizing: border-box;




      .loading-tip {
        text-align: center;
        padding: 0 0 40rpx;
        color: #666;
        font-size: 28rpx;
      }

      .no-more-tip {
        text-align: center;
        padding: 0 0 40rpx;
        color: #999;
        font-size: 26rpx;
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 186rpx 0;
        width: 622rpx;
        text-align: center;
        margin: 0 auto;

        .empty-image {
          width: 448rpx;
          height: 448rpx;
          margin-bottom: 36rpx;
        }

        .empty-title {
          font-size: $font-size-heading-ml;
          font-family: var(--font-family-regular);
          font-weight: $font-weight-bold;
          color: #1d2939;
          line-height: 68rpx;
          margin-top: 100rpx;
        }

        .empty-text {
          font-size: $font-size-heading-sm;
          font-family: var(--font-family-regular);
          font-weight: $font-weight-regular;
          color: #98a2b3;
          margin-top: 32rpx;
        }
      }
    }
  }
}
</style>