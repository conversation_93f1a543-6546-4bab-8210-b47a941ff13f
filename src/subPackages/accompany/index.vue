<script setup>
import { ref } from 'vue'

const bellTipRef = ref(null)
// 当前浮出的人物
const currentPerson = ref('')

function handleSeed() {
  uni.navigateTo({
    url: '/subPackages/accompany/dialogue',
  })
}

// 处理铃铛点击事件
function handleBellClick(val) {
  if (bellTipRef.value) {
    currentPerson.value = val
    bellTipRef.value.show()
  }
}

// 处理铃铛提示关闭事件
function handleBellTipClose() {
  currentPerson.value = ''
}
</script>

<template>
  <view class="page-container">
    <view class="seed-box" @click="handleSeed">
      Send message
    </view>

    <view class="story-container">
      <!-- 月亮 -->
      <Moon />

      <!-- 父亲 -->
      <Father :type="currentPerson" @bell-click="(val) => handleBellClick(val)" />

      <!-- 母亲 -->
      <Mother :type="currentPerson" @bell-click="(val) => handleBellClick(val)" />

      <!-- 孩子 -->
      <Child :type="currentPerson" @bell-click="(val) => handleBellClick(val)" />
    </view>
    <!-- 铃铛提示 -->
    <BellTip ref="bellTipRef" :type="currentPerson" @close="handleBellTipClose" />
  </view>
</template>

<style lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 40rpx;
  font-family: var(--font-family-title);

  image {
    width: 100%;
    height: 100%;
  }

  .seed-box {
    width: 686rpx;
    height: 100rpx;
    border-radius: 32rpx;
    box-shadow: 0px 0px 16px 2px #0000000D;
    line-height: 100rpx;
    box-sizing: border-box;
    padding-left: 32rpx;
    text-align: left;
    font-family: var(--font-family-regular);
    font-size: $font-size-lg;
    font-weight: $font-weight-regular;
    color: #667085;
  }

  .story-container {
    position: relative;
    width: 560rpx;
    height: 780rpx;
    margin-top: 228rpx;
  }
}
</style>
