<template>
  <view class="baby-container">
    <!-- 宝宝卡片 -->
    <BabyCard v-for="(item,index) in 1" :key="index" @add="addBaby" />
    <!-- 朋友圈空组件 -->
    <PostEmptyCard v-if="postItem.length"/> 
    <view class="postList">
      <!-- 朋友圈列表 -->
    <PostCard v-for="(item,index) in 4" :key="index"/>
    </view>
    <!-- <Comment />  -->
     <!-- 按钮 -->
    <!-- <LargeButton @click="payment">Payment</LargeButton>  -->
    <AddBaby :show="addBabyShow" @close="addBabyShow=false"></AddBaby>
  </view>
</template>
<script setup>
// import { getCurrentInstance } from 'vue'
// import { productApi } from '@/api/index'
import BabyCard from './components/baby-card.vue'
import PostCard from './components/post-card.vue'
import PostEmptyCard from './components/post-empty-card.vue'
// import Modal from '@/components/modal/index.vue'
import Comment from './components/comment.vue'
import AddBaby from './components/add-baby.vue'
import {ref} from 'vue'
//朋友圈列表
const postItem = ref([])
const addBabyShow = ref(false)
// 添加宝宝
const addBaby = () => {
  addBabyShow.value = true
}
// import LargeButton from '@/components/button/largeButton.vue'
// px转换
// const instance = getCurrentInstance()
// if (instance) {
//   const pxValue = 100
//   const rpxValue = instance.proxy.$px2rpx(pxValue)
//   console.log(`${pxValue}px = ${rpxValue}rpx`)
// }

// 接口调试
// productApi.list().then(res=>{
//   console.log('联调测试',res)
// })

// baby数组操作
// import { useBabyStore } from '@/stores/baby'
// import { storeToRefs } from 'pinia'
// const store = useBabyStore()
// const { babyList, currentBabyDetail, babyCount } = storeToRefs(store)
// store.setBabyList([newBaby])

const payment = (e) =>{
  console.log('点击了hi', e)
}
</script>
<style lang="scss" scoped>
.baby-container{
  font-family: var(--font-family-light);
  font-size: $font-size-base;
}
.baby-container {
  overflow: auto; /* 确保滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}
/* WebKit 内核补充 */
.baby-container::-webkit-scrollbar {
  display: none;
}
.postList{
  margin-top:20rpx;
}
</style>
