<script setup>
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import { useBabyStore } from '@/stores/baby'
// import Modal from '@/components/modal/index.vue'
// import Comment from './components/comment.vue'
import AddBaby from './components/add-baby.vue'
// import { getCurrentInstance } from 'vue'
// import { productApi } from '@/api/index'
import BabyCard from './components/baby-card.vue'
import PostCard from './components/post-card.vue'
import PostEmptyCard from './components/post-empty-card.vue'
// import { styleText } from 'util'

const babyStore = useBabyStore()
const { safeArea, platform } = storeToRefs(babyStore) // 保持响应式
// 朋友圈列表
const postItem = ref([])
const addBabyShow = ref(false)
// 添加宝宝
function addBaby() {
  addBabyShow.value = true
}
console.log('安全区高度', safeArea.value)
const safeAreaHeight = ref(safeArea.value.height)

// 确认添加宝宝
function submitAddBaby(babyName) {
  addBabyShow.value = false
  if (babyName) {
    uni.navigateTo({
      url: '/subPackages/baby/babyInfo/index',
    })
  }
}
// import LargeButton from '@/components/button/largeButton.vue'
// px转换
// const instance = getCurrentInstance()
// if (instance) {
//   const pxValue = 100
//   const rpxValue = instance.proxy.$px2rpx(pxValue)
//   console.log(`${pxValue}px = ${rpxValue}rpx`)
// }

// 接口调试
// productApi.list().then(res=>{
//   console.log('联调测试',res)
// })

// baby数组操作
// import { useBabyStore } from '@/stores/baby'
// import { storeToRefs } from 'pinia'
// const store = useBabyStore()
// const { babyList, currentBabyDetail, babyCount } = storeToRefs(store)
// store.setBabyList([newBaby])

function payment(e) {
  console.log('点击了hi', e)
}
</script>

<template>
  <view class="baby-container" :style="{ 'padding-top': platform == 'ios' ? '0px' : `${safeArea.height}px` }">
    <view class="top-safe-area" :style="{ height: `${safeArea.height}px` }" />
    <!-- 宝宝卡片 -->
    <BabyCard v-for="(item, index) in 1" :key="index" @add="addBaby" />
    <!-- 朋友圈空组件 -->
    <PostEmptyCard v-if="postItem.length" />
    <view class="postList">
      <!-- 朋友圈列表 -->
      <PostCard v-for="(item, index) in 4" :key="index" />
    </view>
    <!-- <Comment />  -->
    <!-- 按钮 -->
    <!-- <LargeButton @click="payment">Payment</LargeButton>  -->
    <AddBaby :show="addBabyShow" @close="submitAddBaby" />
  </view>
</template>

<style lang="scss" scoped>
.baby-container{
  font-family: var(--font-family-light);
  font-size: $font-size-base;
  padding-top: env(safe-area-inset-top); /* 动态适配刘海屏/状态栏高度 */
  padding-top: constant(safe-area-inset-top); /* 兼容旧版本系统 */
  // padding-top: 100px;
}
.baby-container {
  overflow: auto; /* 确保滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}
/* WebKit 内核补充 */
.baby-container::-webkit-scrollbar {
  display: none;
}
.postList{
  margin-top:20rpx;
}
</style>
