<script setup>
import { ref } from 'vue'
import Navbar from '@/components/navbar/index.vue'
import AddBaby from '@/subPackages/baby/components/add-baby.vue'
import DeleteBtn from '@/subPackages/baby/components/delete-btn.vue'
import RowCard from '@/subPackages/baby/components/row-card.vue'
import babyCryingIcon from '../static/baby/baby-crying.png'
import maternityIcon from '../static/baby/maternity.png'
import AddBabyInfo from './components/add-baby-info.vue'
import MemberCard from './components/member-card.vue'
import ShareAlert from './components/share-alert.vue'
// 用户数据
const userStats = ref({
  visitCount: 12,
  lastVisit: '4 minutes ago',
  unreadNotifications: 2,
})
const showAddBabyInfo = ref(false)
// 功能选项数据
const options = ref([
  {
    emoji: babyCryingIcon,
    title: 'Baby\'s information',
    description: null,
  },
  {
    emoji: maternityIcon,
    title: 'I\'m baby\'s',
    description: 'Mom',
  },
  {
    emoji: maternityIcon,
    title: 'Member Management',
    memberCount: '3 members',
  },
])

// 添加宝宝成员方法
function addMemberFn() {
  // 实际项目中会有更复杂的逻辑
  // #ifdef APP-PLUS
  uni.shareWithSystem({
    summary: 'Accept the invitation: https://example.com',
    href: 'https://example.com',
    imageUrl: '/static/logo.png',
  })
  // #endif
}
function selectRow(index) {
  // baby关系选择
  if (index === 0) {
    uni.navigateTo({
      url: '/subPackages/baby/infoSet/index',
    })
  }
  else if (index === 1) {
    showAddBabyInfo.value = true
  }
  else if (index === 2) {
    uni.navigateTo({
      url: '/subPackages/baby/memberManage/index',
    })
  }
}

// 删除宝宝方法
function deleteBaby() {
  // const confirm = window.confirm("确定要删除宝宝信息吗？此操作无法撤销。");
  if (confirm) {
    // alert("宝宝信息已删除");
  }
}

// 添加宝宝姓名
const addBabyShow = ref(false)
function addBabyName(name) {
  console.log('获取到的宝宝姓名', name)
  addBabyShow.value = false
}
</script>

<template>
  <view>
    <Navbar @click="addBabyShow = true" />
    <view class="app-container">
      <!-- 成员卡片区域 -->
      <view class="members-container flex-row-item">
        <MemberCard :user-stats="userStats" />
        <MemberCard type="add" @add="addMemberFn" />
      </view>
      <!-- 功能选项区域 -->
      <view class="section">
        <!-- 信息选项 -->
        <view v-for="(option, index) in options" :key="index">
          <RowCard :option="option" :index="index" @click="selectRow(index)" />
        </view>
      </view>
      <!-- 删除按钮 -->
      <DeleteBtn title="Delete baby" @click="deleteBaby" />
    </view>
    <!-- 添加宝宝亲属关系 -->
    <AddBabyInfo :show="showAddBabyInfo" @close="showAddBabyInfo = false" />
    <!-- share -->
    <ShareAlert :show="false" />
    <!-- 添加宝宝姓名 -->
    <AddBaby :show="addBabyShow" @close="addBabyName" />
  </view>
</template>

<style lang="scss" scoped>
:v-deep.wd-drop-item__option.is-active{
    color:red !important;
}
.app-container {
    max-width: 500px;
    margin: 0 auto;
}
/* 成员卡片区域 */
.members-container {
    margin: 40rpx 32rpx;
}

/* 功能区域 */
.section {
    padding:32rpx;
}
/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.members-container, .section, .delete-btn {
    animation: fadeIn 0.5s ease-out;
}
</style>
