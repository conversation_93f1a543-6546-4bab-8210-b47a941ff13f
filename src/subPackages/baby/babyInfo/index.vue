<template>
    <view>
        <Navbar />
        <view id="app">
            <view class="app-container">
                <!-- 成员卡片区域 -->
                <view class="members-container flex-row-item">
                    <!-- 妈妈卡片 -->
                    <view class="member-card mom-card">
                        <image></image>
                        <view class="role">Mom</view>
                        <view class="stats">Visited {{ userStats.visitCount }} times</view>
                        <view class="timestamp">{{ userStats.lastVisit }}</view>
                    </view>
                    
                    <!-- 添加成员卡片 -->
                    <view class="member-card add-card" @click="addMember">
                        <!-- <view class="add-icon">+</view> -->
                        <image class="add-icon" src="../static/baby/add-member-icon.png"></image>
                        <view class="stats">Add </view>
                        <view>a member</view>
                    </view>
                </view>
                
                <!-- 功能选项区域 -->
                <view class="section">
                    <!-- 信息选项 -->
                    <view class="function-item flex-row-item justify-between" v-for="(option, index) in options" :key="index" @click="showAddBabyInfo = true">
                        <view class="function-info">
                            <!-- <view class="function-icon">{{ option.emoji }}</view> -->
                            <image  class="function-icon" :src="option.emoji"></image>
                            <view class="function-title">{{ option.title }}</view>
                        </view>
                        <view class="flex-row-item">
                            <view class="member-count" v-if="option.memberCount">{{ option.memberCount }}</view>
                            <view class="arrow">
                                <image src="../static/baby/arrow-right.png"></image>
                            </view>
                        </view>
                    </view>
                </view>
                
                <!-- 删除按钮 -->
                <view class="delete-btn" @click="deleteBaby">
                    Delete baby
                </view>
            </view>
        </view>
        <AddBabyInfo :show="showAddBabyInfo" @close="showAddBabyInfo = false"/>
    </view>
</template>
<script setup>
import { ref } from 'vue' 
import Navbar from '@/components/navbar/index.vue'
import AddBabyInfo from './components/add-baby-info.vue'
// 用户数据
const userStats = ref({
    visitCount: 12,
    lastVisit: "4 minutes ago",
    unreadNotifications: 2
});
import babyCryingIcon from '../static/baby/baby-crying.png'
import maternityIcon from '../static/baby/maternity.png'
const showAddBabyInfo = ref(false)
// 功能选项数据
const options = ref([
    {
    emoji: babyCryingIcon,
    title: "Baby's information",
    description: null
    },
    {
    emoji: maternityIcon,
    title: "I'm baby's",
    description: "Mom"
    },
    {
    emoji:maternityIcon,
    title: "Member Management",
    memberCount: "3 members"
    }
]);

// 添加成员方法
const addMember = () => {
    // 实际项目中会有更复杂的逻辑
    // alert("添加成员功能");
};

// 删除宝宝方法
const deleteBaby = () => {
    // const confirm = window.confirm("确定要删除宝宝信息吗？此操作无法撤销。");
    if (confirm) {
    // alert("宝宝信息已删除");
    }
};
</script>
<style lang="scss" scoped>
:v-deep.wd-drop-item__option.is-active{
    color:red !important;
}
.app-container {
    max-width: 500px;
    margin: 0 auto;
}
/* 成员卡片区域 */
.members-container {
    margin: 40rpx 32rpx;
}

.member-card {
    background-color: $color-white;
    border-radius: 40rpx;
    padding: 32rpx;
    position: relative;
    overflow: hidden;
    width:208rpx;
    height:276rpx;
    margin-right:32rpx;
    color:$color-white;
}

.mom-card {
    background:$color-primary;
    text-align: center;
    image{
        width: 100rpx;
        height: 100rpx;
        background:pink;
        border-radius: 50%;
    }
    .role {
        font-size: $font-size-lg;
        font-weight: 400;
        margin: 20rpx 0 16rpx;
    }
    .stats {
        font-size: $font-size-mdx;
        margin-top: 8px;
    }

    .timestamp {
        font-size: $font-size-base;
        font-weight: 400;
    }
}

.add-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    // justify-content: center;
    transition: all 0.3s ease;
    background:$color-background-light;
    .add-icon {
        width:100rpx;
        height:100rpx;
        margin-bottom: 8px;
        font-weight: bold;
    }
    .stats {
        font-size: $font-size-mdx;
        margin: 20rpx 0 16rpx;
    }
}

/* 功能区域 */
.section {
    padding:32rpx;
    .function-item {
        box-shadow: 5px 4px 5px 0px #9E774D24;
        margin-bottom:40rpx;
        padding:24rpx;
        transition: background-color 0.2s ease;
        border-radius:32rpx;
    }
    .function-icon {
        width: 96rpx;
        height: 96rpx;
        border-radius: 24rpx;
        background-color:#FFF5E3;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
    }
}



.function-item:last-child {
    border-bottom: none;
}

.function-item:hover {
    background-color: #f9f9f9;
}

.function-info {
    display: flex;
    align-items: center;
}



.function-title {
    font-size: $font-size-lg;
    font-family:var(--font-family-title);
}

.member-count {
    font-size: $font-size-base;
    color: #667085;
}

.arrow {
    color: #999;
    image{
        width: 24rpx;
        height: 24rpx;
        margin-left:12rpx;
    }
}

/* 删除按钮 */
.delete-btn {
    background: linear-gradient(92.74deg, #FF7773 9.43%, #FF4473 96.54%);
    color: $color-white;
    padding:28rpx 0;
    width:calc(100% - 74rpx);
    margin:0 37rpx;
    border-radius: 100rpx;
    text-align:center;
    position:fixed;
    bottom:46px;
}

.delete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.members-container, .section, .delete-btn {
    animation: fadeIn 0.5s ease-out;
}

.function-item {
    animation-duration: 0.4s;
    animation-timing-function: ease-out;
    animation-fill-mode: both;
}

.function-item:nth-child(1) {
    animation-delay: 0.1s;
    animation-name: fadeIn;
}

.function-item:nth-child(2) {
    animation-delay: 0.2s;
    animation-name: fadeIn;
}

.function-item:nth-child(3) {
    animation-delay: 0.3s;
    animation-name: fadeIn;
}
</style>
