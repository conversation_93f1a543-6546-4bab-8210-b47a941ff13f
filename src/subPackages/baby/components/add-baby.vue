<template>
    <view v-if="show" class="add-baby">
        <view class="mask" @click="submitBabyName"></view>
        <wd-floating-panel class="high-z-index" :contentDraggable="false" :safeAreaInsetBottom="true">
            <view class="inner-content">
                 <view class="input-box">
                    <input class="myInput" ref="myInput" type="text" v-model="inputValue" placeholder="Add a baby" />
                </view>
            </view>
        </wd-floating-panel>
    </view>
</template>
<script setup>
import { ref } from 'vue' 
import { defineProps, defineEmits } from 'vue'
const props = defineProps({
    // 展示弹窗
    show: {
        type: Boolean,
        default: false
    }
})
const emit = defineEmits(['close']);
const inputValue = ref('')
const submitBabyName = () => {
    emit('close')
    if(inputValue.value){
        //提交baby名字
        uni.navigateTo({
          url: '/subPackages/baby/babyInfo/index'
        })
    }
}
</script>
<style lang="scss" scoped>
.mask{
    background:rgba(0,0,0,.3);
    width:100%;
    height:100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index:999;
}
.high-z-index{
    z-index: 9999;
}
.input-box{
    background-color: #eee;
    border-radius: 30rpx;
    width: max-content;
    height: 60rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: calc(100% - 88rpx);
    margin: 0 44rpx;
    padding:0 0 0 24rpx;
    z-index:9999;
    .myInput{
        flex:1;
    }
}

</style>