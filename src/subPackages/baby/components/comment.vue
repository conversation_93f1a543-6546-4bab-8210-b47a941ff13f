<!-- 评论组件 -->
<script setup>
import { storeToRefs } from 'pinia'
import { defineEmits, defineProps, nextTick, ref } from 'vue'
import { useBabyStore } from '@/stores/baby'
import CommentList from './comment-list.vue'

// 保持响应式

const props = defineProps({
  // 展示弹窗
  visable: {
    type: Boolean,
    default: false,
  },
})
// 关闭弹窗
const emit = defineEmits(['closeComment'])
const babyStore = useBabyStore()
const { platform } = storeToRefs(babyStore)
const systemInfo = uni.getSystemInfoSync()
// 输入框的值
const inputValue = ref('')

const focusInput = ref(false)
const keyboardHeight = ref(9999)
// 评论数据
const comments = ref([
  {
    id: 1,
    username: '<PERSON>ry<PERSON><PERSON>',
    avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
    timestamp: 'September 19',
    content: 'The baby did a interesting behavior!!!!',
    likes: 24,
    liked: false,
    child: [{
      id: 109,
      username: '<PERSON><PERSON><PERSON><PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
      timestamp: 'September 19',
      content: 'The baby did a interesting behavior!!!!',
      likes: 24,
      liked: false,
    }],
  },
  {
    id: 2,
    username: 'Emma',
    avatar: 'https://randomuser.me/api/portraits/women/42.jpg',
    timestamp: 'September 20',
    content: 'ohhh~',
    likes: 18,
    liked: true,
  },
  {
    id: 3,
    username: 'Kitty',
    avatar: 'https://randomuser.me/api/portraits/women/56.jpg',
    timestamp: 'September 19',
    content: 'This is so cute!',
    likes: 45,
    liked: false,
  },
])
function closeComment() {
  // subPackages/baby
  emit('closeComment')
}
// 点赞/取消点赞功能
function toggleLike(commentId) {
  const comment = comments.value.find(c => c.id === commentId)
  if (comment) {
    if (comment.liked) {
      comment.likes--
    }
    else {
      comment.likes++
    }
    comment.liked = !comment.liked
  }
}

const setTimer = ref(null)

// 监听键盘高度变化
function onKeyboardHeightChange(e) {
  if (setTimer.value) {
    clearTimeout(setTimer)
    setTimer.value = null
  }
  setTimer.value = setTimeout(() => {
    console.log('键盘的高度', e)
    if (e.detail.height) {
      keyboardHeight.value = e.detail.height - systemInfo.safeAreaInsets.bottom - (platform.value == 'ios' ? systemInfo.statusBarHeight / 3 * 2 : 0)
    }
    else {
      focusInput.value = false
      keyboardHeight.value = 9999
    }
  }, 200)
}

function inputBlur(e) {
  focusInput.value = false
  setTimer.value = setTimeout(() => {
    keyboardHeight.value = 9999
    console.log('键盘失焦', e)
  }, 500)
}

async function inputClick() {
  await nextTick() // 等待 DOM 更新
  focusInput.value = true
}

// 显示更多选项
function showOptions(commentId) {
  // 实际项目中这里会弹出选项菜单
  console.log('显示评论选项:', commentId)
}

// 提交评论
function postComment() {
  focusInput.value = false
  console.log('发送')
  uni.navigateTo({
    url: '/subPackages/baby/playVideo?url=http://xinliushijie.oss-cn-beijing.aliyuncs.com/upload/common/video/20250708/20250708022952175195619222017.mp4',
  })
  // if (inputValue.value.trim()) {
  // 生成新的评论对象
  // const newComment = {
  //     id: Date.now(), // 使用时间戳作为唯一ID
  //     username: "Current User",
  //     avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  //     timestamp: "Just now",
  //     content: inputValue.value,
  //     likes: 0,
  //     liked: false
  // }

  // // 添加到评论列表
  // comments.value.unshift(newComment)

  // // 清空输入框
  // inputValue.value = ''
  // }
}
</script>

<template>
  <view v-if="visable" class="commentAlert">
    <view class="mask" @click="closeComment" />
    <!-- 评论区主容器 -->
    <view v-if="visable" class="comment-container">
      <!-- 顶部标题 -->
      <view class="comment-header">
        <h1>Comment</h1>
        <view class="close" @click.stop="closeComment">
          <wd-icon name="close" size="16px" color="#98A2B3" />
        </view>
      </view>
      <!-- 评论列表 -->
      <view class="comment-list">
        <!-- 循环渲染评论 -->
        <view v-for="item in comments" :key="item.id">
          <comment-list :comment="item" />
          <view v-for="childComment in item.child" :key="childComment.id" class="childComment">
            <comment-list :comment="childComment" />
          </view>
        </view>
      </view>
      <!-- 底部输入框 -->
      <view class="comment-input-container flex-row-item justify-between">
        <!-- 输入框 -->
        <view class="input-box" @click="inputClick">
          {{ inputValue || '请输入' }}
          <!-- <textarea  class="comment-input" v-model="inputValue" name="" id="" rows="1"  placeholder="comment"></textarea> -->
        </view>
        <!-- 发送按钮 -->
        <view class="send" @click="postComment">
          Send
        </view>
      </view>
    </view>
    <!-- 悬浮输入框 -->
    <view v-if="focusInput" class="mask" style="z-index: 999;" />
    <view v-if="focusInput" class="pupInput flex-row-item justify-between" :style="{ bottom: `${keyboardHeight}px` }"@click="inputClick">
      <input ref="myInput" v-model="inputValue" class="myInput" type="text" :focus="focusInput" @keyboardheightchange="onKeyboardHeightChange" @blur="inputBlur">
      <view @click="postComment">
        Send
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.pupInput{
    position: fixed;
    left: 0;
    display: flex;
    align-items: center;
    z-index: 999;
    width:100%;
    background:$color-white;
    // padding:24rpx 32rpx;
    padding:24rpx 32rpx 34rpx 24rpx;
    border-top: 1px solid #f0f0f0;
    .myInput{
        background-color: #eee;
        flex:1;
        height:76rpx;
        border-radius:40rpx;
        padding:0 32rpx;
    }
    view{
        width:200rpx;
        text-align: center;
    }
}
.commentAlert{
    text-align:left;
}
.childComment{
    margin-left:68rpx;
}
.mask{
    background:rgba(0,0,0,.3);
    width:100%;
    height:100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index:100;
}
.close{
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 32rpx;
    height: 100%;
    display: flex;
    align-items: center;
}
.comment-container {
    background-color: white;
    border-radius: 32rpx 32rpx 0 0;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    position: fixed;
    bottom: 0;
    z-index: 998;
}

.comment-header {
    padding: 36rpx 40rpx;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
    position: relative;
}

.comment-header h1 {
    font-size:$font-size-lg;
    font-weight: 400;
    color: #1D2129;
    font-family: var(--font-family-title);
}

.comment-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 40rpx;
}

.like-action:hover {
    color: #FF4444;
}

.comment-input-container {
    padding: 30rpx;
    background-color: $color-white;
    .input-box{
        flex:1;
        background-color: #eee;
        border-radius: 30rpx;
        // min-width: 100rpx;
        width: max-content;
        // max-width: 200rpx;
        height: 60rpx;
        box-sizing: border-box;
        display: flex;
        // justify-content: center;
        align-items: center;
        padding: 0 20rpx;
    }
    .send{
        margin-left: 20rpx;
    }
}
:v-deep.wd-input{
    background:#EDEDED;
}
.comment-input {
    border-radius: 40rpx;
    padding: 0 32rpx;
    background:#EDEDED;
    transition: all 0.3s ease;
    // max-height: 90px;
    height:76rpx;
    line-height: 76rpx;
}

.comment-input:focus-within {
    background-color: #fff;
    box-shadow: 0 0 0 1px $auxiliary-orange-light;
}

/* 滚动条样式 */
.comment-list::-webkit-scrollbar {
    width: 12rpx;
}

.comment-list::-webkit-scrollbar-thumb {
    background-color: rgba(188, 188, 188, 0.5);
    border-radius: 6rpx;
}
</style>
