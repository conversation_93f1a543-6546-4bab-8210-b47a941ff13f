import { defineStore } from 'pinia'
import type { Baby } from '@/types/baby'  // 类型定义文件
import {ref, computed} from 'vue'
export const useBabyStore = defineStore('baby', () => {
  // 类型定义
  interface Baby {
    id: string | number
    name: string
    birthDate: Date
    gender?: 'male' | 'female'
    // 其他婴儿属性...
  }

  // 状态声明
  const babyList = ref<Baby[]>([])  // 婴儿列表
  const currentBabyDetail = ref<Baby | null>(null)  // 当前选中婴儿

  // 操作方法
  const setBabyList = (list: Baby[]) => {
    babyList.value = list
    console.log('当前获取到最新的baby', babyList)
  }

  const addBaby = (baby: Baby) => {
    babyList.value.push(baby)
    console.log('添加baby后获取到的list', babyList)
  }

  const updateBaby = (id: string | number, updates: Partial<Baby>) => {
    const index = babyList.value.findIndex(b => b.id === id)
    if (index !== -1) {
      babyList.value[index] = { ...babyList.value[index], ...updates }
      // 同步更新当前选中项
      if (currentBabyDetail.value?.id === id) {
        currentBabyDetail.value = { ...currentBabyDetail.value, ...updates }
      }
    }
  }

  const removeBaby = (id: string | number) => {
    const index = babyList.value.findIndex(b => b.id === id)
    if (index !== -1) {
      babyList.value.splice(index, 1)
      // 如果删除的是当前选中项，则清空
      if (currentBabyDetail.value?.id === id) {
        currentBabyDetail.value = null
      }
    }
  }

  const setCurrentBaby = (baby: Baby | null) => {
    currentBabyDetail.value = baby
  }

  const findBabyById = (id: string | number) => {
    return babyList.value.find(baby => baby.id === id)
  }

  // 计算属性
  const babyCount = computed(() => babyList.value.length)

  return {
    // 状态
    babyList,
    currentBabyDetail,
    
    // 方法
    setBabyList,
    addBaby,
    updateBaby,
    removeBaby,
    setCurrentBaby,
    findBabyById,
    
    // 计算属性
    babyCount
  }
})