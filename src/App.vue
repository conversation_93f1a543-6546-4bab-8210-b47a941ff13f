<script setup lang="ts">
import { onLaunch } from '@dcloudio/uni-app'
import { useBabyStore } from '@/stores/baby'

const babyStore = useBabyStore()

onLaunch(() => {
  const systemInfo = uni.getSystemInfoSync()
  const topSafeHeight = systemInfo.safeAreaInsets.top || systemInfo.statusBarHeight
  babyStore.safeArea = { height: topSafeHeight }

  const platform = systemInfo.platform // 返回值: 'ios'、'android' 或 'devtools'
  if (platform === 'ios') {
    console.log('当前是 iOS 设备')
    // 执行 iOS 专属逻辑（如调用原生插件）
    babyStore.platform = 'ios'
  }
  else if (platform === 'android') {
    console.log('当前是 Android 设备')
    // 执行 Android 专属逻辑
    babyStore.platform = 'android'
  }
  else {
    console.log('其他平台:', platform) // 开发工具中通常为 'devtools'
    babyStore.platform = 'other'
  }
})
</script>

<style lang="scss">
@import "@/styles/global.scss";
@import "@/styles/global-fonts.scss";
body{
    color:#262626;
    font-family: var(--font-family-light);
}
// body {
//   padding-top: constant(safe-area-inset-top); /* 兼容 iOS < 11.2 */
//   padding-top: env(safe-area-inset-top);     /* 兼容 iOS >= 11.2 */
// }
.app-container { 
  padding-top: constant(safe-area-inset-top); /* 兼容 iOS < 11.2 */
  padding-top: env(safe-area-inset-top);     /* 兼容 iOS >= 11.2 */
  height: 100vh;
  box-sizing: border-box;
}
.top-safe-area {
    position: fixed;
    top:0;
    left:0;
    width:100%;
    background-color:#fff;
    z-index:999;
}
</style>
