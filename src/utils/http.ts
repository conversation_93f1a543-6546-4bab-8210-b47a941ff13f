// utils/http.js
const env = process.env.NODE_ENV || 'development'
const BASE_URL = {
  development: 'http://192.168.112.194:8084/api',
  test: 'https://xinliushijie.guest.geekdanceshop.com/api',
  production: 'https://xinliushijie.guest.geekdanceshop.com/api',
}[env]

// 全局请求队列（用于取消请求）
const pendingRequests = new Map()

export default function request(options) {
  const {
    url,
    method = 'GET',
    data = {},
    header = {},
    hideLoading = false,
  } = options

  // 生成唯一请求标识（用于取消重复请求）
  const requestKey = `${method}${url}${JSON.stringify(data)}`

  // 取消重复请求
  if (pendingRequests.has(requestKey)) {
    pendingRequests.get(requestKey).abort()
  }

  // 显示加载状态
  if (!hideLoading)
    uni.showLoading({ title: '加载中', mask: true })

  return new Promise((resolve, reject) => {
    // 请求拦截：注入Token
    const token = JSON.parse(uni.getStorageSync('token') || "null" )
    const headers = {
      'Content-Type': 'application/json',
      ...(token && { 'userToken': token }),
      ...header,
    }

    // 创建请求任务
    const task = uni.request({
      url: BASE_URL + url,
      method: method.toUpperCase(),
      data,
      header: headers,
      success: (res) => {
        pendingRequests.delete(requestKey)
        if (!hideLoading)
          uni.hideLoading()

        // 响应拦截：统一处理状态码
        handleResponse(res, resolve, reject)
      },
      fail: (err) => {
        pendingRequests.delete(requestKey)
        if (!hideLoading)
          uni.hideLoading()
        handleNetworkError(err, reject)
      },
    })

    // 存储请求任务
    pendingRequests.set(requestKey, task)
  })
}

// 响应处理逻辑
function handleResponse(res, resolve, reject) {
  if (res.statusCode >= 200 && res.statusCode < 300) {
    // 业务状态码处理（示例：1表示成功）
    if (res.data?.code === 1) {
      resolve(res.data.data)
    }
    else {
      uni.showToast({ title: res.data.msg || '业务异常', icon: 'none' })
      reject(res.data)
    }
  }
  else if (res.statusCode === 401) {
    uni.navigateTo({ url: '/subPackages/login/index' })
    reject(new Error('未授权'))
  }
  else {
    uni.showToast({ title: `HTTP错误: ${res.statusCode}`, icon: 'none' })
    reject(new Error(`HTTP ${res.statusCode}`))
  }
}

// 网络错误处理
function handleNetworkError(err, reject) {
  const errorMap = {
    'request:fail timeout': '请求超时',
    'request:fail interrupted': '请求被取消',
  }
  const msg = errorMap[err.errMsg] || '网络连接失败'
  uni.showToast({ title: msg, icon: 'none' })
  reject(err)
}

// 衍生快捷方法
export function get(url, params, options) {
  return request({ url, method: 'GET', data: params, ...options })
}

export function post(url, data, options) {
  return request({ url, method: 'POST', data, ...options })
}

export function uploadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: BASE_URL + url,
      filePath,
      name: 'file',
      header: { 'userToken': JSON.parse(uni.getStorageSync('token')) },
      success: res => handleResponse(res, resolve, reject),
      fail: err => handleNetworkError(err, reject),
    })
  })
}
