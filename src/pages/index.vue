<script setup lang="ts">
import { ref } from 'vue'
import TabBar from '@/components/custom-tab-bar/TabBar.vue'
import Baby from '../subPackages/baby/index.vue'
import Accompany from '../subPackages/accompany/index.vue'
import Store from '../subPackages/store/index.vue'
import Mine from '../subPackages/mine/index.vue'

// 组件映射
const components = {
  Baby,
  Accompany,
  Store,
  Mine,
}

// 当前选中的索引
const selectIndex = ref(0)

// 定义 scrollFn 函数并指定事件参数类型为 Event
function scrollFn(e: Event) {
  const target = e.target as HTMLElement
  // 判断是否滑动到底部
  if (target.scrollTop + target.clientHeight >= target.scrollHeight) {
    console.log('滑动到底部了')
  }
}

// Tab 切换处理函数
const selectTabBar = (item: any) => {
  console.log("selectTabBar", item)
  if(item.isSpecial){
    // 点击中间的➕号
  }else{
  selectIndex.value = item.tabIndex // 更新选中的索引
  }
}

</script>

<template>
  <view class="root-container app-container"> 
    <div class="home-container" @scroll="scrollFn">
      <!-- 使用动态组件 -->
      <component :is="components[Object.keys(components)[selectIndex]]" />
    </div>
    <!-- 传递 selectTabBar 函数和 selectIndex 给 TabBar 组件 -->
    <TabBar :select="selectTabBar" :safeAreaBottom="0" :selectedIndex="selectIndex" />
  </view>
</template>

<style scoped>
.root-container {
  text-align: center;
  /* background-color: blue; */
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom:150rpx;
}
.home-container {
  flex-grow: 1;
  /* background-color: red; */
  overflow-y: scroll;
}
</style>
